"use client";

import React, { useState, useRef } from "react";
import { Card, Input, Alert, Typography, Upload, Button, Space, Spin, message } from "antd";
import { InboxOutlined, FileTextOutlined, CloseOutlined, FolderOpenOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";

const { Title, Text } = Typography;
const { Dragger } = Upload;

interface FileUploadCardProps {
  onFileSelect: (filepath: string) => void;
  error?: string;
  isLoading?: boolean;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({ onFileSelect, error, isLoading = false }) => {
  const [filepath, setFilepath] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (value: string) => {
    setFilepath(value);
    onFileSelect(value);
  };

  const clearInput = () => {
    setFilepath("");
    onFileSelect("");
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Get the full path if available (works in Electron/desktop environments)
      // Otherwise fallback to just the filename
      const path = file.name;
      handleInputChange(path);
      message.success(`Selected: ${path}`);
    }
  };

  const uploadProps: UploadProps = {
    name: "file",
    multiple: false,
    accept: ".edf",
    showUploadList: false,
    beforeUpload: (file) => {
      // Prevent default upload behavior
      // Instead, extract the file path
      const fileName = file.name;
      if (fileName) {
        if (!fileName.endsWith(".edf")) {
          message.error("Please select an EDF file");
          return false;
        }
        handleInputChange(fileName);
        message.success(`Selected: ${fileName}`);
      }
      return false; // Prevent upload
    },
    onDrop(e) {
      setIsDragging(false);
      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        const file = files[0];
        const fileName = file.name;
        if (fileName) {
          if (!fileName.endsWith(".edf")) {
            message.error("Please select an EDF file");
            return;
          }
          handleInputChange(fileName);
        }
      }
    },
  };

  return (
    <Card
      className="max-w-4xl w-full mx-auto shadow-lg border border-gray-200"
      styles={{
        body: { padding: "40px" },
      }}
    >
      <Spin spinning={isLoading}>
        <Space direction="vertical" size="large" className="w-full">
          {/* Header */}
          <div className="text-center mb-6">
            <Title level={2} className="!mb-3 text-gray-900">
              File Selection
            </Title>
            <Text type="secondary" className="text-lg">
              Select an EDF file to begin analysis configuration
            </Text>
          </div>

          {/* Upload Area */}
          <div className={`relative mb-6 ${isDragging ? "opacity-75" : ""}`}>
            <Dragger
              {...uploadProps}
              disabled={isLoading}
              className={`!bg-gray-50 hover:!bg-gray-100 ${isDragging ? "!border-blue-500 !border-2" : "!border-gray-300"} !rounded-lg`}
              style={{
                background: isDragging ? "#f8fafc" : "#fafafa",
                borderStyle: isDragging ? "solid" : "dashed",
                minHeight: "200px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div className="text-center py-8">
                <p className="ant-upload-drag-icon mb-4">
                  <InboxOutlined style={{ fontSize: 64, color: isDragging ? "#3b82f6" : "#9ca3af" }} />
                </p>
                <p className="ant-upload-text text-xl font-semibold text-gray-700 mb-2">Click or drag EDF file to this area</p>
                <p className="ant-upload-hint text-gray-500 text-base">
                  Support for single EDF file upload. The file should contain EEG recording data.
                </p>
              </div>
            </Dragger>
          </div>

          {/* Hidden file input for native file selection */}
          <input ref={fileInputRef} type="file" accept=".edf" onChange={handleFileInputChange} style={{ display: "none" }} />

          {/* File Path Input */}
          <div className="mb-6">
            <Text strong className="block mb-3 text-gray-800 text-base">
              Or enter file path directly:
            </Text>
            <Space.Compact style={{ width: "100%" }}>
              <Input
                size="large"
                value={filepath}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="/path/to/file.edf"
                disabled={isLoading}
                prefix={<FileTextOutlined className="text-gray-400" />}
                suffix={
                  filepath && !isLoading ? (
                    <CloseOutlined className="cursor-pointer text-gray-400 hover:text-gray-600 transition-colors" onClick={clearInput} />
                  ) : null
                }
                className="!py-3 text-base"
              />
              <Button size="large" icon={<FolderOpenOutlined />} onClick={handleFileSelect} disabled={isLoading} className="px-6">
                Browse
              </Button>
            </Space.Compact>
          </div>

          {/* Error Alert */}
          {error && <Alert message="Error" description={error} type="error" showIcon closable />}

          {/* File Selected Indicator */}
          {filepath && !error && (
            <Alert
              message="File Selected"
              description={
                <Space>
                  <FileTextOutlined />
                  <Text code>{filepath}</Text>
                </Space>
              }
              type="success"
              showIcon
            />
          )}

          {/* Instructions */}
          <Card
            type="inner"
            className="bg-blue-50 border-blue-200 mt-6"
            styles={{
              body: { padding: "20px" },
            }}
          >
            <Space direction="vertical" size="small" className="w-full">
              <Text strong className="text-blue-800 text-base">
                Requirements:
              </Text>
              <ul className="list-disc list-inside text-sm text-blue-700 space-y-2 ml-2">
                <li>File must be in EDF format (.edf extension)</li>
                <li>Minimum sampling rate of 200 Hz required</li>
                <li>File should contain valid EEG channel data</li>
                <li>Ensure file path is accessible from the application</li>
              </ul>
            </Space>
          </Card>

          {/* Action Button (optional - for manual trigger) */}
          {filepath && !isLoading && !error && (
            <Button type="primary" size="large" block className="!bg-blue-600 !border-blue-600 hover:!bg-blue-700 !h-12 !text-lg !font-semibold mt-6">
              Continue to Parameter Settings
            </Button>
          )}
        </Space>
      </Spin>
    </Card>
  );
};

export default FileUploadCard;
