"use client";

import { useState, useCallback } from "react";
import { Layout, Steps, Typography, Space, Breadcrumb } from "antd";
import { FileTextOutlined, SettingOutlined, LineChartOutlined, HomeOutlined } from "@ant-design/icons";
import EEGViewer from "@/components/EEGViewer";
import FileUploadCard from "@/components/FileUploadCard";
import ParameterSettingsCard from "@/components/ParameterSettingsCard";
import { WebSocketProvider } from "@/contexts/WebSocketContext";
import { AnalysisParameters, FileInfo, DEFAULT_PARAMETERS } from "@/types/eeg";
import { getFileInfo, startAnalysis } from "@/utils/api";

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;
const { Step } = Steps;

type AppState = "file-selection" | "settings-configuration" | "analysis";

export default function Home() {
  const [appState, setAppState] = useState<AppState>("file-selection");
  const [filepath, setFilepath] = useState("");
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [parameters, setParameters] = useState<AnalysisParameters>(DEFAULT_PARAMETERS);
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleFileSelect = async (selectedFilepath: string) => {
    setFilepath(selectedFilepath);
    setError("");

    if (!selectedFilepath) {
      setFileInfo(null);
      return;
    }

    try {
      setIsLoading(true);
      const info = await getFileInfo(selectedFilepath);
      setFileInfo(info);
      setAppState("settings-configuration");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load file information");
      setFileInfo(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleParametersChange = useCallback((newParameters: AnalysisParameters) => {
    setParameters(newParameters);
  }, []);

  const handleValidationChange = useCallback((valid: boolean) => {
    setIsValid(valid);
  }, []);

  const handleStartAnalysis = async () => {
    if (!filepath || !fileInfo || !isValid) {
      setError("Please ensure all parameters are valid before starting analysis");
      return;
    }

    try {
      setError("");
      setIsLoading(true);
      await startAnalysis(filepath, parameters);
      setAppState("analysis");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to start analysis");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToFileSelection = () => {
    setAppState("file-selection");
    setFileInfo(null);
    setFilepath("");
    setParameters(DEFAULT_PARAMETERS);
    setIsValid(false);
    setError("");
  };

  const getCurrentStep = () => {
    switch (appState) {
      case "file-selection":
        return 0;
      case "settings-configuration":
        return 1;
      case "analysis":
        return 2;
      default:
        return 0;
    }
  };

  const getBreadcrumbItems = () => {
    const items = [
      {
        title: (
          <Space size="small">
            <HomeOutlined />
            <span>Home</span>
          </Space>
        ),
      },
    ];

    if (appState === "settings-configuration" || appState === "analysis") {
      items.push({
        title: <span>File Selection</span>,
      });
    }

    if (appState === "settings-configuration") {
      items.push({
        title: <span>Parameter Configuration</span>,
      });
    }

    if (appState === "analysis") {
      items.push({
        title: <span>Parameter Configuration</span>,
      });
      items.push({
        title: <span>Analysis</span>,
      });
    }

    return items;
  };

  const renderCurrentStep = () => {
    switch (appState) {
      case "file-selection":
        return <FileUploadCard onFileSelect={handleFileSelect} error={error} isLoading={isLoading} />;

      case "settings-configuration":
        return fileInfo ? (
          <ParameterSettingsCard
            fileInfo={fileInfo}
            onParametersChange={handleParametersChange}
            onValidationChange={handleValidationChange}
            onStartAnalysis={handleStartAnalysis}
            onBack={handleBackToFileSelection}
            initialParameters={parameters}
          />
        ) : null;

      case "analysis":
        return (
          <WebSocketProvider>
            <EEGViewer />
          </WebSocketProvider>
        );

      default:
        return null;
    }
  };

  return (
    <Layout className="min-h-screen">
      <Header className="bg-white border-b border-gray-200 px-6 md:px-8 h-16 flex items-center">
        <div className="flex items-center justify-between h-full w-full max-w-7xl mx-auto">
          <Space size="large">
            <Title level={3} className="!mb-0 flex items-center gap-2 text-gray-900">
              <LineChartOutlined className="text-blue-600" />
              Biormika HFO Detector
            </Title>
          </Space>
          <div className="text-sm text-gray-500 hidden md:block">Real-time High-Frequency Oscillation Detection</div>
        </div>
      </Header>

      <Content className="bg-gray-50 flex-1">
        <div className="max-w-7xl mx-auto px-6 md:px-8 py-8">
          {/* Breadcrumb */}
          <Breadcrumb items={getBreadcrumbItems()} className="mb-8" />

          {/* Steps Progress */}
          {appState !== "analysis" && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-8">
              <div className="mb-4">
                <Title level={4} className="!mb-2 text-gray-900">
                  Analysis Setup
                </Title>
                <Text type="secondary" className="text-base">
                  Follow these steps to configure your HFO analysis
                </Text>
              </div>
              <Steps current={getCurrentStep()} size="default" className="mt-6">
                <Step title="File Selection" description="Select EDF file for analysis" icon={<FileTextOutlined />} />
                <Step title="Configure Parameters" description="Set detection parameters" icon={<SettingOutlined />} />
                <Step title="Analysis" description="View results in real-time" icon={<LineChartOutlined />} />
              </Steps>
            </div>
          )}

          {/* Main Content */}
          <div className="animate-fadeIn flex justify-center w-full">{renderCurrentStep()}</div>
        </div>
      </Content>

      <Footer className="text-center bg-white border-t border-gray-200 py-6">
        <div className="max-w-7xl mx-auto px-6 md:px-8">
          <Space direction="vertical" size="small">
            <div className="text-gray-600 font-medium">Biormika HFO Detector © {new Date().getFullYear()}</div>
            <div className="text-sm text-gray-400">High-Frequency Oscillation Detection for EEG Analysis</div>
          </Space>
        </div>
      </Footer>
    </Layout>
  );
}
